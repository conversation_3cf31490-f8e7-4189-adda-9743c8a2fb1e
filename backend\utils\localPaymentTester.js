const axios = require('axios');
const User = require('../models/userModel');
const Subscription = require('../models/subscriptionModel');
const Plan = require('../models/planModel');

/**
 * LOCAL PAYMENT TESTING SYSTEM
 * 
 * This utility provides local payment testing capabilities for development:
 * 1. Simulates payment flows without external API dependencies
 * 2. Tests payment cancellation scenarios
 * 3. Validates account activation prevention logic
 * 4. Provides mock payment responses for testing
 */

class LocalPaymentTester {
  constructor() {
    this.isLocalMode = process.env.NODE_ENV === 'development' || process.env.PAYMENT_DEMO_MODE === 'true';
    this.baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
    this.testUsers = [];
    this.testSubscriptions = [];
  }

  /**
   * Initialize test environment
   */
  async initializeTestEnvironment() {
    if (!this.isLocalMode) {
      console.log('⚠️ Local payment testing is only available in development mode');
      return false;
    }

    console.log('🧪 Initializing Local Payment Test Environment...');
    
    try {
      // Create test users if they don't exist
      await this.createTestUsers();
      
      // Create test plans if they don't exist
      await this.createTestPlans();
      
      console.log('✅ Local payment test environment initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize test environment:', error.message);
      return false;
    }
  }

  /**
   * Create test users for payment testing
   */
  async createTestUsers() {
    const testUserData = [
      {
        firstName: 'Test',
        lastName: 'User1',
        username: 'testuser1',
        email: '<EMAIL>',
        phoneNumber: '0712345678',
        password: 'testpassword123',
        paymentRequired: true
      },
      {
        firstName: 'Test',
        lastName: 'User2',
        username: 'testuser2',
        email: '<EMAIL>',
        phoneNumber: '0712345679',
        password: 'testpassword123',
        paymentRequired: true
      }
    ];

    for (const userData of testUserData) {
      try {
        const existingUser = await User.findOne({ username: userData.username });
        if (!existingUser) {
          const bcrypt = require('bcrypt');
          const salt = await bcrypt.genSalt(10);
          userData.password = await bcrypt.hash(userData.password, salt);
          
          const user = new User(userData);
          await user.save();
          this.testUsers.push(user);
          console.log(`✅ Created test user: ${userData.username}`);
        } else {
          this.testUsers.push(existingUser);
          console.log(`ℹ️ Test user already exists: ${userData.username}`);
        }
      } catch (error) {
        console.error(`❌ Error creating test user ${userData.username}:`, error.message);
      }
    }
  }

  /**
   * Create test plans for payment testing
   */
  async createTestPlans() {
    const testPlanData = [
      {
        title: 'Test Basic Plan',
        description: 'Basic plan for testing',
        price: 5000,
        discountedPrice: 4000,
        duration: 1,
        features: ['Basic access', 'Test features'],
        isActive: true
      },
      {
        title: 'Test Premium Plan',
        description: 'Premium plan for testing',
        price: 10000,
        discountedPrice: 8000,
        duration: 3,
        features: ['Premium access', 'All test features'],
        isActive: true
      }
    ];

    for (const planData of testPlanData) {
      try {
        const existingPlan = await Plan.findOne({ title: planData.title });
        if (!existingPlan) {
          const plan = new Plan(planData);
          await plan.save();
          console.log(`✅ Created test plan: ${planData.title}`);
        } else {
          console.log(`ℹ️ Test plan already exists: ${planData.title}`);
        }
      } catch (error) {
        console.error(`❌ Error creating test plan ${planData.title}:`, error.message);
      }
    }
  }

  /**
   * Simulate a payment initiation
   */
  async simulatePaymentInitiation(userId, planId) {
    if (!this.isLocalMode) {
      throw new Error('Payment simulation only available in local development mode');
    }

    console.log('🧪 Simulating payment initiation...');

    try {
      const user = await User.findById(userId);
      const plan = await Plan.findById(planId);

      if (!user || !plan) {
        throw new Error('User or plan not found');
      }

      // Generate mock order ID
      const orderId = `ORDER_TEST_${Date.now()}_${user._id.toString().slice(-6)}`;

      // Create subscription with pending status
      const subscription = new Subscription({
        user: user._id,
        activePlan: plan._id,
        paymentStatus: 'pending',
        status: 'pending',
        paymentHistory: [{
          orderId: orderId,
          plan: plan._id,
          amount: plan.discountedPrice,
          paymentStatus: 'pending',
          paymentDate: new Date().toISOString().split('T')[0],
          paymentMethod: 'ZenoPay_Test'
        }]
      });

      await subscription.save();
      this.testSubscriptions.push(subscription);

      console.log(`✅ Mock payment initiated for ${user.firstName} ${user.lastName}`);
      console.log(`📋 Order ID: ${orderId}`);
      console.log(`💰 Amount: ${plan.discountedPrice} TZS`);

      return {
        success: true,
        orderId: orderId,
        subscriptionId: subscription._id,
        amount: plan.discountedPrice,
        message: 'Mock payment initiated successfully'
      };

    } catch (error) {
      console.error('❌ Error simulating payment initiation:', error.message);
      throw error;
    }
  }

  /**
   * Simulate payment completion
   */
  async simulatePaymentCompletion(orderId) {
    if (!this.isLocalMode) {
      throw new Error('Payment simulation only available in local development mode');
    }

    console.log(`🧪 Simulating payment completion for order: ${orderId}`);

    try {
      const webhookPayload = {
        order_id: orderId,
        payment_status: 'COMPLETED',
        reference: `TEST_REF_${Date.now()}`,
        amount: '5000',
        currency: 'TZS',
        buyer_name: 'Test User',
        buyer_phone: '+255712345678',
        buyer_email: '<EMAIL>',
        timestamp: new Date().toISOString()
      };

      // Send webhook to local server
      const response = await axios.post(`${this.baseUrl}/api/payment/webhook`, webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY
        },
        timeout: 10000
      });

      console.log('✅ Payment completion webhook sent successfully');
      return response.data;

    } catch (error) {
      console.error('❌ Error simulating payment completion:', error.message);
      throw error;
    }
  }

  /**
   * Simulate payment cancellation
   */
  async simulatePaymentCancellation(orderId) {
    if (!this.isLocalMode) {
      throw new Error('Payment simulation only available in local development mode');
    }

    console.log(`🧪 Simulating payment cancellation for order: ${orderId}`);

    try {
      const webhookPayload = {
        order_id: orderId,
        payment_status: 'CANCELLED',
        reference: `CANCELLED_REF_${Date.now()}`,
        amount: '5000',
        currency: 'TZS',
        buyer_name: 'Test User',
        buyer_phone: '+255712345678',
        buyer_email: '<EMAIL>',
        timestamp: new Date().toISOString(),
        cancellation_reason: 'User cancelled payment'
      };

      // Send webhook to local server
      const response = await axios.post(`${this.baseUrl}/api/payment/webhook`, webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY
        },
        timeout: 10000
      });

      console.log('✅ Payment cancellation webhook sent successfully');
      return response.data;

    } catch (error) {
      console.error('❌ Error simulating payment cancellation:', error.message);
      throw error;
    }
  }

  /**
   * Simulate payment failure
   */
  async simulatePaymentFailure(orderId) {
    if (!this.isLocalMode) {
      throw new Error('Payment simulation only available in local development mode');
    }

    console.log(`🧪 Simulating payment failure for order: ${orderId}`);

    try {
      const webhookPayload = {
        order_id: orderId,
        payment_status: 'FAILED',
        reference: `FAILED_REF_${Date.now()}`,
        amount: '5000',
        currency: 'TZS',
        buyer_name: 'Test User',
        buyer_phone: '+255712345678',
        buyer_email: '<EMAIL>',
        timestamp: new Date().toISOString(),
        failure_reason: 'Insufficient funds'
      };

      // Send webhook to local server
      const response = await axios.post(`${this.baseUrl}/api/payment/webhook`, webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY
        },
        timeout: 10000
      });

      console.log('✅ Payment failure webhook sent successfully');
      return response.data;

    } catch (error) {
      console.error('❌ Error simulating payment failure:', error.message);
      throw error;
    }
  }

  /**
   * Run comprehensive payment security tests
   */
  async runPaymentSecurityTests() {
    if (!this.isLocalMode) {
      console.log('⚠️ Payment security tests only available in development mode');
      return;
    }

    console.log('🔒 Running Payment Security Tests...');
    console.log('=====================================');

    try {
      await this.initializeTestEnvironment();

      if (this.testUsers.length === 0) {
        console.log('❌ No test users available for testing');
        return;
      }

      const testUser = this.testUsers[0];
      const testPlan = await Plan.findOne({ title: 'Test Basic Plan' });

      if (!testPlan) {
        console.log('❌ No test plan available for testing');
        return;
      }

      // Test 1: Payment initiation
      console.log('\n1️⃣ Testing payment initiation...');
      const paymentResult = await this.simulatePaymentInitiation(testUser._id, testPlan._id);
      const orderId = paymentResult.orderId;

      // Test 2: Payment cancellation (should not activate account)
      console.log('\n2️⃣ Testing payment cancellation...');
      await this.simulatePaymentCancellation(orderId);
      
      // Check if account was activated (should not be)
      await this.checkAccountActivationStatus(testUser._id, 'CANCELLED');

      // Test 3: Payment failure (should not activate account)
      console.log('\n3️⃣ Testing payment failure...');
      const failureResult = await this.simulatePaymentInitiation(testUser._id, testPlan._id);
      await this.simulatePaymentFailure(failureResult.orderId);
      
      // Check if account was activated (should not be)
      await this.checkAccountActivationStatus(testUser._id, 'FAILED');

      // Test 4: Payment completion (should require manual activation)
      console.log('\n4️⃣ Testing payment completion...');
      const completionResult = await this.simulatePaymentInitiation(testUser._id, testPlan._id);
      await this.simulatePaymentCompletion(completionResult.orderId);
      
      // Check if account was activated (should be pending manual approval)
      await this.checkAccountActivationStatus(testUser._id, 'COMPLETED');

      console.log('\n✅ Payment security tests completed');

    } catch (error) {
      console.error('❌ Payment security tests failed:', error.message);
    }
  }

  /**
   * Check account activation status after payment
   */
  async checkAccountActivationStatus(userId, paymentStatus) {
    try {
      const user = await User.findById(userId);
      const subscription = await Subscription.findOne({ user: userId }).sort({ createdAt: -1 });

      console.log(`📊 Account Status Check (${paymentStatus}):`);
      console.log(`   User Payment Required: ${user.paymentRequired}`);
      console.log(`   User Subscription Status: ${user.subscriptionStatus}`);
      console.log(`   Subscription Status: ${subscription?.status || 'N/A'}`);
      console.log(`   Subscription Payment Status: ${subscription?.paymentStatus || 'N/A'}`);

      if (paymentStatus === 'CANCELLED' || paymentStatus === 'FAILED') {
        if (user.subscriptionStatus === 'active' || subscription?.status === 'active') {
          console.log('❌ SECURITY ISSUE: Account was activated despite cancelled/failed payment!');
        } else {
          console.log('✅ SECURITY OK: Account was not activated for cancelled/failed payment');
        }
      } else if (paymentStatus === 'COMPLETED') {
        if (user.subscriptionStatus === 'active' || subscription?.status === 'active') {
          console.log('⚠️ WARNING: Account was auto-activated. Manual approval should be required.');
        } else {
          console.log('✅ SECURITY OK: Account requires manual activation even for completed payment');
        }
      }

    } catch (error) {
      console.error('❌ Error checking account activation status:', error.message);
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    if (!this.isLocalMode) {
      return;
    }

    console.log('🧹 Cleaning up test data...');

    try {
      // Remove test subscriptions
      await Subscription.deleteMany({ 
        'paymentHistory.orderId': { $regex: /^ORDER_TEST_/ }
      });

      // Remove test users
      await User.deleteMany({ 
        username: { $in: ['testuser1', 'testuser2'] }
      });

      // Remove test plans
      await Plan.deleteMany({ 
        title: { $in: ['Test Basic Plan', 'Test Premium Plan'] }
      });

      console.log('✅ Test data cleaned up successfully');

    } catch (error) {
      console.error('❌ Error cleaning up test data:', error.message);
    }
  }
}

// Export singleton instance
const localPaymentTester = new LocalPaymentTester();
module.exports = localPaymentTester;
