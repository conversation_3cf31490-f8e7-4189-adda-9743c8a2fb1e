const chalk = require('chalk');
const mongoose = require('mongoose');
const express = require('express');
require('dotenv').config();

// Import utilities
const localPaymentTester = require('./utils/localPaymentTester');
const activationGuards = require('./utils/activationGuards');
const paymentValidator = require('./utils/paymentValidator');

/**
 * LOCAL DEVELOPMENT STARTUP SCRIPT
 * 
 * This script starts the application in local development mode with:
 * 1. Enhanced payment security
 * 2. Local payment testing capabilities
 * 3. Account activation guards
 * 4. Comprehensive security validation
 */

class LocalDevelopmentServer {
  constructor() {
    this.app = null;
    this.server = null;
    this.port = process.env.PORT || 5000;
  }

  /**
   * Start the local development server
   */
  async start() {
    try {
      console.log(chalk.blue('🚀 BRAINWAVE LOCAL DEVELOPMENT SERVER'));
      console.log(chalk.blue('====================================='));
      console.log('');

      // Validate environment
      await this.validateEnvironment();

      // Connect to database
      await this.connectToDatabase();

      // Initialize security systems
      await this.initializeSecuritySystems();

      // Start the main server
      await this.startMainServer();

      // Run initial security checks
      await this.runInitialSecurityChecks();

      // Display startup information
      this.displayStartupInfo();

    } catch (error) {
      console.error(chalk.red('❌ Failed to start local development server:'), error.message);
      process.exit(1);
    }
  }

  /**
   * Validate environment configuration
   */
  async validateEnvironment() {
    console.log(chalk.yellow('🔧 Validating environment configuration...'));

    const requiredEnvVars = [
      'NODE_ENV',
      'MONGO_URL',
      'JWT_SECRET',
      'CLIENT_URL',
      'ZENOPAY_API_KEY'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Validate development mode
    if (process.env.NODE_ENV !== 'development') {
      console.log(chalk.yellow('⚠️ Warning: NODE_ENV is not set to "development"'));
    }

    // Validate local URLs
    if (!process.env.CLIENT_URL.includes('localhost')) {
      console.log(chalk.yellow('⚠️ Warning: CLIENT_URL does not appear to be local'));
    }

    console.log(chalk.green('✅ Environment validation passed'));
  }

  /**
   * Connect to database
   */
  async connectToDatabase() {
    console.log(chalk.yellow('🗄️ Connecting to MongoDB...'));

    try {
      await mongoose.connect(process.env.MONGO_URL, {
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        family: 4
      });

      console.log(chalk.green('✅ MongoDB connected successfully'));
    } catch (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Initialize security systems
   */
  async initializeSecuritySystems() {
    console.log(chalk.yellow('🛡️ Initializing security systems...'));

    try {
      // Initialize local payment tester
      const paymentTesterInitialized = await localPaymentTester.initializeTestEnvironment();
      if (paymentTesterInitialized) {
        console.log(chalk.green('✅ Local payment tester initialized'));
      } else {
        console.log(chalk.yellow('⚠️ Local payment tester initialization skipped'));
      }

      // Test activation guards
      console.log(chalk.blue('🧪 Testing activation guards...'));
      // Guards are tested when needed, no initialization required

      console.log(chalk.green('✅ Security systems initialized'));
    } catch (error) {
      throw new Error(`Security system initialization failed: ${error.message}`);
    }
  }

  /**
   * Start the main server
   */
  async startMainServer() {
    console.log(chalk.yellow('🌐 Starting main application server...'));

    try {
      // Import and start the main server
      const serverModule = require('./server');
      
      // The server.js file already handles the startup, so we just need to wait
      await new Promise((resolve) => {
        setTimeout(resolve, 3000); // Wait for server to start
      });

      console.log(chalk.green('✅ Main application server started'));
    } catch (error) {
      throw new Error(`Main server startup failed: ${error.message}`);
    }
  }

  /**
   * Run initial security checks
   */
  async runInitialSecurityChecks() {
    console.log(chalk.yellow('🔍 Running initial security checks...'));

    try {
      // Check for any incorrectly activated accounts
      const auditResult = await activationGuards.auditAndRollbackIncorrectActivations();
      
      if (auditResult.success) {
        if (auditResult.incorrectActivations > 0) {
          console.log(chalk.red(`⚠️ Found ${auditResult.incorrectActivations} incorrectly activated accounts`));
          console.log(chalk.blue('🔄 Automatic rollback initiated'));
        } else {
          console.log(chalk.green('✅ No incorrectly activated accounts found'));
        }
      } else {
        console.log(chalk.yellow('⚠️ Security audit failed, manual review recommended'));
      }

      console.log(chalk.green('✅ Initial security checks completed'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️ Security checks failed: ${error.message}`));
    }
  }

  /**
   * Display startup information
   */
  displayStartupInfo() {
    console.log('');
    console.log(chalk.green('🎉 LOCAL DEVELOPMENT SERVER READY!'));
    console.log(chalk.green('==================================='));
    console.log('');
    console.log(chalk.blue('📍 Server Information:'));
    console.log(`   🌐 Backend URL: ${process.env.BACKEND_URL || `http://localhost:${this.port}`}`);
    console.log(`   🎨 Frontend URL: ${process.env.CLIENT_URL}`);
    console.log(`   🗄️ Database: ${process.env.MONGO_URL.includes('localhost') ? 'Local' : 'Remote'}`);
    console.log(`   🧪 Environment: ${process.env.NODE_ENV}`);
    console.log('');
    console.log(chalk.blue('🔒 Security Features:'));
    console.log('   ✅ Payment validation with ZenoPay API');
    console.log('   ✅ Account activation guards');
    console.log('   ✅ Cancelled payment prevention');
    console.log('   ✅ Failed payment prevention');
    console.log('   ✅ Manual activation requirement');
    console.log('   ✅ Security audit and rollback');
    console.log('');
    console.log(chalk.blue('🧪 Testing Features:'));
    console.log('   ✅ Local payment simulation');
    console.log('   ✅ Payment cancellation testing');
    console.log('   ✅ Payment failure testing');
    console.log('   ✅ Comprehensive security tests');
    console.log('');
    console.log(chalk.blue('🔗 Available Endpoints:'));
    console.log(`   📊 Health Check: ${process.env.BACKEND_URL || `http://localhost:${this.port}`}/api/health`);
    console.log(`   💳 Payment Webhook: ${process.env.BACKEND_URL || `http://localhost:${this.port}`}/api/payment/webhook`);
    console.log(`   🧪 Test Payment: Run 'node test-payment-security-comprehensive.js'`);
    console.log('');
    console.log(chalk.blue('🛠️ Development Commands:'));
    console.log('   🧪 Run security tests: npm run test:security');
    console.log('   🔄 Test payment flows: npm run test:payments');
    console.log('   🛡️ Audit activations: npm run audit:activations');
    console.log('');
    console.log(chalk.yellow('⚠️ IMPORTANT SECURITY NOTES:'));
    console.log('   🚫 Cancelled payments will NEVER activate accounts');
    console.log('   ❌ Failed payments will NEVER activate accounts');
    console.log('   🔒 Completed payments require manual admin approval');
    console.log('   🛡️ All activation attempts are logged and audited');
    console.log('   🔄 Incorrect activations are automatically rolled back');
    console.log('');
    console.log(chalk.green('✨ Ready for secure local development!'));
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log(chalk.yellow('\n🔄 Shutting down local development server...'));

    try {
      // Close database connection
      await mongoose.disconnect();
      console.log(chalk.green('✅ Database disconnected'));

      // Close server if running
      if (this.server) {
        this.server.close();
        console.log(chalk.green('✅ Server closed'));
      }

      console.log(chalk.green('✅ Local development server shut down successfully'));
      process.exit(0);
    } catch (error) {
      console.error(chalk.red('❌ Error during shutdown:'), error.message);
      process.exit(1);
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  const server = new LocalDevelopmentServer();
  await server.shutdown();
});

process.on('SIGTERM', async () => {
  const server = new LocalDevelopmentServer();
  await server.shutdown();
});

// Start server if this file is executed directly
if (require.main === module) {
  const server = new LocalDevelopmentServer();
  server.start().catch(console.error);
}

module.exports = LocalDevelopmentServer;
