const axios = require('axios');
require('dotenv').config();

/**
 * ENHANCED PAYMENT VALIDATION UTILITY
 * 
 * This utility provides secure payment validation that prevents
 * subscription activation for cancelled or failed payments.
 * 
 * SECURITY FEATURES:
 * - Always verifies with ZenoPay API before activation
 * - Prevents activation of cancelled/failed payments
 * - Logs all validation attempts for audit
 * - Requires manual activation for security
 */

class PaymentValidator {
  constructor() {
    this.apiKey = process.env.ZENOPAY_API_KEY;
    this.baseUrl = 'https://api.zenoapi.com/api/v1';
    this.timeout = 15000;
  }

  /**
   * Validate payment status with ZenoPay API
   * @param {string} orderId - The order ID to validate
   * @returns {Object} Validation result
   */
  async validatePayment(orderId) {
    try {
      console.log(`🔍 Validating payment: ${orderId}`);

      if (!orderId || !orderId.startsWith('ORDER_')) {
        return {
          valid: false,
          status: 'INVALID',
          reason: 'invalid_order_id',
          message: 'Order ID is invalid or missing'
        };
      }

      // SECURITY: Check for test orders in development mode
      if (orderId.includes('_TEST_') && process.env.NODE_ENV === 'development') {
        console.log('🧪 Test order detected in development mode');
        return {
          valid: true,
          status: 'COMPLETED',
          reason: 'test_payment',
          message: 'Test payment validated for development',
          paymentData: {
            orderId: orderId,
            status: 'COMPLETED',
            amount: 5000,
            currency: 'TZS',
            testMode: true
          }
        };
      }

      if (!this.apiKey) {
        console.error('❌ ZenoPay API key not configured');
        return {
          valid: false,
          status: 'ERROR',
          reason: 'api_key_missing',
          message: 'ZenoPay API key not configured'
        };
      }

      const response = await axios.get(`${this.baseUrl}/payments/${orderId}`, {
        headers: {
          'x-api-key': this.apiKey,
        },
        timeout: this.timeout
      });

      console.log(`📊 ZenoPay API response for ${orderId}:`, response.data);

      if (response.data && response.data.data && response.data.data.length > 0) {
        const paymentData = response.data.data[0];
        const status = paymentData.payment_status;

        console.log(`💳 Payment status: ${status}`);

        switch (status) {
          case 'COMPLETED':
            console.log(`✅ Payment ${orderId} is COMPLETED`);
            return {
              valid: true,
              status: 'COMPLETED',
              reason: 'payment_completed',
              message: 'Payment successfully completed',
              paymentData: paymentData
            };

          case 'CANCELLED':
            console.log(`🚫 SECURITY: Payment ${orderId} was CANCELLED - activation permanently blocked`);
            return {
              valid: false,
              status: 'CANCELLED',
              reason: 'payment_cancelled',
              message: 'Payment was cancelled by user',
              paymentData: paymentData,
              blockActivation: true,
              securityFlag: 'CANCELLED_PAYMENT',
              preventActivation: true
            };

          case 'FAILED':
            console.log(`❌ SECURITY: Payment ${orderId} FAILED - activation permanently blocked`);
            return {
              valid: false,
              status: 'FAILED',
              reason: 'payment_failed',
              message: 'Payment failed to process',
              paymentData: paymentData,
              blockActivation: true,
              securityFlag: 'FAILED_PAYMENT',
              preventActivation: true
            };

          case 'PENDING':
            console.log(`⏳ Payment ${orderId} is still PENDING`);
            return {
              valid: false,
              status: 'PENDING',
              reason: 'payment_pending',
              message: 'Payment is still being processed',
              paymentData: paymentData
            };

          default:
            console.log(`⚠️ SECURITY: Unknown payment status: ${status} - activation blocked`);
            return {
              valid: false,
              status: status,
              blockActivation: true,
              securityFlag: 'UNKNOWN_STATUS',
              preventActivation: true,
              reason: 'unknown_status',
              message: `Unknown payment status: ${status}`,
              paymentData: paymentData
            };
        }
      } else {
        console.log(`⚠️ No payment data found for ${orderId}`);
        return {
          valid: false,
          status: 'NOT_FOUND',
          reason: 'payment_not_found',
          message: 'Payment not found in ZenoPay system'
        };
      }

    } catch (error) {
      console.error(`❌ Error validating payment ${orderId}:`, error.message);
      
      if (error.response) {
        console.error(`📊 API Error Response:`, error.response.data);
        return {
          valid: false,
          status: 'API_ERROR',
          reason: 'api_error',
          message: `ZenoPay API error: ${error.response.status}`,
          error: error.response.data
        };
      } else if (error.code === 'ECONNABORTED') {
        return {
          valid: false,
          status: 'TIMEOUT',
          reason: 'api_timeout',
          message: 'ZenoPay API request timed out'
        };
      } else {
        return {
          valid: false,
          status: 'ERROR',
          reason: 'network_error',
          message: `Network error: ${error.message}`
        };
      }
    }
  }

  /**
   * Validate subscription before activation
   * @param {Object} subscription - The subscription to validate
   * @returns {Object} Validation result
   */
  async validateSubscriptionForActivation(subscription) {
    try {
      console.log(`🔍 Validating subscription for activation: ${subscription._id}`);

      if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
        return {
          canActivate: false,
          reason: 'no_payment_history',
          message: 'No payment history found for subscription'
        };
      }

      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      const orderId = latestPayment.orderId;

      if (!orderId) {
        return {
          canActivate: false,
          reason: 'no_order_id',
          message: 'No order ID found in payment history'
        };
      }

      // Validate payment with ZenoPay API
      const validationResult = await this.validatePayment(orderId);

      if (validationResult.valid && validationResult.status === 'COMPLETED') {
        console.log(`✅ Subscription ${subscription._id} payment is valid for activation`);
        return {
          canActivate: true,
          reason: 'payment_verified',
          message: 'Payment verified and completed',
          orderId: orderId,
          paymentData: validationResult.paymentData
        };
      } else {
        console.log(`❌ Subscription ${subscription._id} cannot be activated: ${validationResult.reason}`);
        return {
          canActivate: false,
          reason: validationResult.reason,
          message: validationResult.message,
          orderId: orderId,
          paymentStatus: validationResult.status
        };
      }

    } catch (error) {
      console.error(`❌ Error validating subscription ${subscription._id}:`, error.message);
      return {
        canActivate: false,
        reason: 'validation_error',
        message: `Validation error: ${error.message}`
      };
    }
  }

  /**
   * Check if a payment was cancelled or failed
   * @param {string} orderId - The order ID to check
   * @returns {boolean} True if payment was cancelled or failed
   */
  async isPaymentCancelledOrFailed(orderId) {
    try {
      const validationResult = await this.validatePayment(orderId);
      return validationResult.status === 'CANCELLED' || validationResult.status === 'FAILED';
    } catch (error) {
      console.error(`❌ Error checking payment status:`, error.message);
      return false; // Assume not cancelled if we can't verify
    }
  }

  /**
   * Log validation attempt for audit purposes
   * @param {string} orderId - The order ID
   * @param {Object} result - The validation result
   * @param {string} action - The action being attempted
   */
  logValidationAttempt(orderId, result, action = 'validation') {
    const timestamp = new Date().toISOString();
    console.log(`📝 AUDIT LOG [${timestamp}]: ${action.toUpperCase()}`);
    console.log(`   Order ID: ${orderId}`);
    console.log(`   Valid: ${result.valid || result.canActivate}`);
    console.log(`   Status: ${result.status || result.reason}`);
    console.log(`   Message: ${result.message}`);
    
    // In production, this should be logged to a secure audit log
    // For now, we'll just console log for visibility
  }
}

// Export singleton instance
const paymentValidator = new PaymentValidator();
module.exports = paymentValidator;
