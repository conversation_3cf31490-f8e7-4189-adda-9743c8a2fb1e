const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');
const localPaymentTester = require('./utils/localPaymentTester');
const activationGuards = require('./utils/activationGuards');
const paymentValidator = require('./utils/paymentValidator');

/**
 * COMPREHENSIVE PAYMENT SECURITY TEST SUITE
 * 
 * This test suite validates that:
 * 1. Cancelled payments NEVER activate accounts
 * 2. Failed payments NEVER activate accounts
 * 3. Only completed payments can be activated (with manual approval)
 * 4. Activation guards work correctly
 * 5. Local payment testing system works
 * 6. Security rollback functionality works
 */

class ComprehensivePaymentSecurityTester {
  constructor() {
    this.baseUrl = process.env.BACKEND_URL || 'http://localhost:5000';
    this.testResults = [];
    this.testUsers = [];
    this.testSubscriptions = [];
  }

  /**
   * Run all comprehensive security tests
   */
  async runAllTests() {
    console.log('🔒 COMPREHENSIVE PAYMENT SECURITY TEST SUITE');
    console.log('==============================================');
    console.log(`🌐 Testing against: ${this.baseUrl}`);
    console.log(`🗄️ Database: ${process.env.MONGO_URL ? 'Connected' : 'Not configured'}`);
    console.log(`🧪 Environment: ${process.env.NODE_ENV}`);
    console.log('');

    try {
      // Connect to database
      await this.connectToDatabase();

      // Initialize test environment
      await this.initializeTestEnvironment();

      // Test 1: Cancelled Payment Security
      await this.testCancelledPaymentSecurity();

      // Test 2: Failed Payment Security
      await this.testFailedPaymentSecurity();

      // Test 3: Completed Payment Flow
      await this.testCompletedPaymentFlow();

      // Test 4: Activation Guards
      await this.testActivationGuards();

      // Test 5: Security Rollback
      await this.testSecurityRollback();

      // Test 6: Audit and Detection
      await this.testAuditAndDetection();

      // Display comprehensive results
      this.displayTestResults();

      // Cleanup
      await this.cleanup();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      console.error(error.stack);
    } finally {
      await mongoose.disconnect();
    }
  }

  /**
   * Connect to database
   */
  async connectToDatabase() {
    try {
      await mongoose.connect(process.env.MONGO_URL);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Initialize test environment
   */
  async initializeTestEnvironment() {
    console.log('\n🔧 Initializing test environment...');
    
    try {
      // Initialize local payment tester
      const initialized = await localPaymentTester.initializeTestEnvironment();
      if (!initialized) {
        throw new Error('Failed to initialize local payment tester');
      }

      // Create additional test users for comprehensive testing
      await this.createTestUsers();
      
      console.log('✅ Test environment initialized');
    } catch (error) {
      throw new Error(`Test environment initialization failed: ${error.message}`);
    }
  }

  /**
   * Test 1: Cancelled Payment Security
   */
  async testCancelledPaymentSecurity() {
    console.log('\n1️⃣ TESTING CANCELLED PAYMENT SECURITY');
    console.log('=====================================');

    try {
      const testUser = await User.findOne({ username: 'testuser1' });
      const testPlan = await Plan.findOne({ title: 'Test Basic Plan' });

      if (!testUser || !testPlan) {
        throw new Error('Test user or plan not found');
      }

      // Step 1: Initiate payment
      console.log('📤 Step 1: Initiating payment...');
      const paymentResult = await localPaymentTester.simulatePaymentInitiation(testUser._id, testPlan._id);
      const orderId = paymentResult.orderId;

      // Step 2: Cancel payment
      console.log('🚫 Step 2: Cancelling payment...');
      await localPaymentTester.simulatePaymentCancellation(orderId);

      // Step 3: Wait for webhook processing
      await this.wait(2000);

      // Step 4: Verify account was NOT activated
      console.log('🔍 Step 3: Verifying account was NOT activated...');
      const updatedUser = await User.findById(testUser._id);
      const subscription = await Subscription.findOne({ user: testUser._id }).sort({ createdAt: -1 });

      const testResult = {
        test: 'Cancelled Payment Security',
        orderId: orderId,
        userActivated: updatedUser.subscriptionStatus === 'active',
        subscriptionActive: subscription?.status === 'active',
        paymentStatus: subscription?.paymentStatus,
        activationBlocked: subscription?.activationBlocked,
        blockReason: subscription?.blockReason,
        securityFlag: subscription?.securityFlag
      };

      if (testResult.userActivated || testResult.subscriptionActive) {
        testResult.result = 'FAILED';
        testResult.issue = 'Account was activated despite cancelled payment';
        console.log('❌ TEST FAILED: Account was activated despite cancelled payment!');
      } else {
        testResult.result = 'PASSED';
        console.log('✅ TEST PASSED: Account was NOT activated for cancelled payment');
      }

      this.testResults.push(testResult);

    } catch (error) {
      console.error('❌ Cancelled payment test failed:', error.message);
      this.testResults.push({
        test: 'Cancelled Payment Security',
        result: 'ERROR',
        error: error.message
      });
    }
  }

  /**
   * Test 2: Failed Payment Security
   */
  async testFailedPaymentSecurity() {
    console.log('\n2️⃣ TESTING FAILED PAYMENT SECURITY');
    console.log('==================================');

    try {
      const testUser = await User.findOne({ username: 'testuser2' });
      const testPlan = await Plan.findOne({ title: 'Test Basic Plan' });

      if (!testUser || !testPlan) {
        throw new Error('Test user or plan not found');
      }

      // Step 1: Initiate payment
      console.log('📤 Step 1: Initiating payment...');
      const paymentResult = await localPaymentTester.simulatePaymentInitiation(testUser._id, testPlan._id);
      const orderId = paymentResult.orderId;

      // Step 2: Fail payment
      console.log('❌ Step 2: Failing payment...');
      await localPaymentTester.simulatePaymentFailure(orderId);

      // Step 3: Wait for webhook processing
      await this.wait(2000);

      // Step 4: Verify account was NOT activated
      console.log('🔍 Step 3: Verifying account was NOT activated...');
      const updatedUser = await User.findById(testUser._id);
      const subscription = await Subscription.findOne({ user: testUser._id }).sort({ createdAt: -1 });

      const testResult = {
        test: 'Failed Payment Security',
        orderId: orderId,
        userActivated: updatedUser.subscriptionStatus === 'active',
        subscriptionActive: subscription?.status === 'active',
        paymentStatus: subscription?.paymentStatus,
        activationBlocked: subscription?.activationBlocked,
        blockReason: subscription?.blockReason,
        securityFlag: subscription?.securityFlag
      };

      if (testResult.userActivated || testResult.subscriptionActive) {
        testResult.result = 'FAILED';
        testResult.issue = 'Account was activated despite failed payment';
        console.log('❌ TEST FAILED: Account was activated despite failed payment!');
      } else {
        testResult.result = 'PASSED';
        console.log('✅ TEST PASSED: Account was NOT activated for failed payment');
      }

      this.testResults.push(testResult);

    } catch (error) {
      console.error('❌ Failed payment test failed:', error.message);
      this.testResults.push({
        test: 'Failed Payment Security',
        result: 'ERROR',
        error: error.message
      });
    }
  }

  /**
   * Test 3: Completed Payment Flow
   */
  async testCompletedPaymentFlow() {
    console.log('\n3️⃣ TESTING COMPLETED PAYMENT FLOW');
    console.log('=================================');

    try {
      const testUser = await User.findOne({ username: 'testuser1' });
      const testPlan = await Plan.findOne({ title: 'Test Premium Plan' });

      if (!testUser || !testPlan) {
        throw new Error('Test user or plan not found');
      }

      // Step 1: Initiate payment
      console.log('📤 Step 1: Initiating payment...');
      const paymentResult = await localPaymentTester.simulatePaymentInitiation(testUser._id, testPlan._id);
      const orderId = paymentResult.orderId;

      // Step 2: Complete payment
      console.log('✅ Step 2: Completing payment...');
      await localPaymentTester.simulatePaymentCompletion(orderId);

      // Step 3: Wait for webhook processing
      await this.wait(2000);

      // Step 4: Verify payment is verified but requires manual activation
      console.log('🔍 Step 3: Verifying payment verification and manual approval requirement...');
      const updatedUser = await User.findById(testUser._id);
      const subscription = await Subscription.findOne({ user: testUser._id }).sort({ createdAt: -1 });

      const testResult = {
        test: 'Completed Payment Flow',
        orderId: orderId,
        paymentVerified: subscription?.paymentVerified,
        userActivated: updatedUser.subscriptionStatus === 'active',
        subscriptionActive: subscription?.status === 'active',
        paymentStatus: subscription?.paymentStatus,
        requiresManualApproval: subscription?.status === 'pending' && subscription?.paymentVerified
      };

      if (testResult.userActivated || testResult.subscriptionActive) {
        testResult.result = 'WARNING';
        testResult.issue = 'Account was auto-activated. Manual approval should be required.';
        console.log('⚠️ TEST WARNING: Account was auto-activated. Manual approval should be required.');
      } else if (testResult.paymentVerified && testResult.requiresManualApproval) {
        testResult.result = 'PASSED';
        console.log('✅ TEST PASSED: Payment verified but requires manual activation');
      } else {
        testResult.result = 'FAILED';
        testResult.issue = 'Payment verification failed';
        console.log('❌ TEST FAILED: Payment verification failed');
      }

      this.testResults.push(testResult);

    } catch (error) {
      console.error('❌ Completed payment test failed:', error.message);
      this.testResults.push({
        test: 'Completed Payment Flow',
        result: 'ERROR',
        error: error.message
      });
    }
  }

  /**
   * Test 4: Activation Guards
   */
  async testActivationGuards() {
    console.log('\n4️⃣ TESTING ACTIVATION GUARDS');
    console.log('============================');

    try {
      // Test with a subscription that has cancelled payment
      const subscription = await Subscription.findOne({ 
        activationBlocked: true,
        blockReason: { $exists: true }
      });

      if (!subscription) {
        console.log('ℹ️ No blocked subscription found for guard testing');
        return;
      }

      console.log('🛡️ Testing activation guards on blocked subscription...');
      const guardResult = await activationGuards.checkActivationAllowed(subscription);

      const testResult = {
        test: 'Activation Guards',
        subscriptionId: subscription._id,
        guardAllowed: guardResult.allowed,
        guardCode: guardResult.code,
        guardMessage: guardResult.message
      };

      if (guardResult.allowed) {
        testResult.result = 'FAILED';
        testResult.issue = 'Guards allowed activation of blocked subscription';
        console.log('❌ TEST FAILED: Guards allowed activation of blocked subscription');
      } else {
        testResult.result = 'PASSED';
        console.log('✅ TEST PASSED: Guards correctly blocked activation');
      }

      this.testResults.push(testResult);

    } catch (error) {
      console.error('❌ Activation guards test failed:', error.message);
      this.testResults.push({
        test: 'Activation Guards',
        result: 'ERROR',
        error: error.message
      });
    }
  }

  /**
   * Test 5: Security Rollback
   */
  async testSecurityRollback() {
    console.log('\n5️⃣ TESTING SECURITY ROLLBACK');
    console.log('============================');

    try {
      // Find any active subscription for rollback testing
      const activeSubscription = await Subscription.findOne({ 
        status: 'active',
        paymentStatus: 'paid'
      });

      if (!activeSubscription) {
        console.log('ℹ️ No active subscription found for rollback testing');
        return;
      }

      console.log('🔄 Testing security rollback...');
      const rollbackResult = await activationGuards.rollbackActivation(
        activeSubscription._id, 
        'security_test_rollback'
      );

      const testResult = {
        test: 'Security Rollback',
        subscriptionId: activeSubscription._id,
        rollbackSuccess: rollbackResult.success,
        rollbackMessage: rollbackResult.message
      };

      if (rollbackResult.success) {
        testResult.result = 'PASSED';
        console.log('✅ TEST PASSED: Security rollback worked correctly');
      } else {
        testResult.result = 'FAILED';
        testResult.issue = rollbackResult.message;
        console.log('❌ TEST FAILED: Security rollback failed');
      }

      this.testResults.push(testResult);

    } catch (error) {
      console.error('❌ Security rollback test failed:', error.message);
      this.testResults.push({
        test: 'Security Rollback',
        result: 'ERROR',
        error: error.message
      });
    }
  }

  /**
   * Test 6: Audit and Detection
   */
  async testAuditAndDetection() {
    console.log('\n6️⃣ TESTING AUDIT AND DETECTION');
    console.log('==============================');

    try {
      console.log('🔍 Running audit for incorrect activations...');
      const auditResult = await activationGuards.auditAndRollbackIncorrectActivations();

      const testResult = {
        test: 'Audit and Detection',
        auditSuccess: auditResult.success,
        incorrectActivations: auditResult.incorrectActivations || 0,
        rollbackResults: auditResult.rollbackResults || []
      };

      if (auditResult.success) {
        testResult.result = 'PASSED';
        console.log(`✅ TEST PASSED: Audit completed. Found ${testResult.incorrectActivations} incorrect activations`);
      } else {
        testResult.result = 'FAILED';
        testResult.issue = auditResult.message;
        console.log('❌ TEST FAILED: Audit failed');
      }

      this.testResults.push(testResult);

    } catch (error) {
      console.error('❌ Audit test failed:', error.message);
      this.testResults.push({
        test: 'Audit and Detection',
        result: 'ERROR',
        error: error.message
      });
    }
  }

  /**
   * Create additional test users
   */
  async createTestUsers() {
    // Test users are created by localPaymentTester.initializeTestEnvironment()
    console.log('ℹ️ Test users created by local payment tester');
  }

  /**
   * Display comprehensive test results
   */
  displayTestResults() {
    console.log('\n📊 COMPREHENSIVE TEST RESULTS');
    console.log('=============================');

    let passed = 0;
    let failed = 0;
    let warnings = 0;
    let errors = 0;

    this.testResults.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.test}: ${result.result}`);
      
      if (result.result === 'PASSED') passed++;
      else if (result.result === 'FAILED') failed++;
      else if (result.result === 'WARNING') warnings++;
      else if (result.result === 'ERROR') errors++;

      if (result.issue) {
        console.log(`   Issue: ${result.issue}`);
      }
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.orderId) {
        console.log(`   Order ID: ${result.orderId}`);
      }
    });

    console.log('\n📈 SUMMARY');
    console.log('==========');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`🔥 Errors: ${errors}`);
    console.log(`📊 Total Tests: ${this.testResults.length}`);

    if (failed === 0 && errors === 0) {
      console.log('\n🎉 ALL SECURITY TESTS PASSED! Payment security is working correctly.');
    } else {
      console.log('\n🚨 SECURITY ISSUES DETECTED! Please review failed tests.');
    }
  }

  /**
   * Cleanup test data
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');
    try {
      await localPaymentTester.cleanupTestData();
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup failed:', error.message);
    }
  }

  /**
   * Wait for specified milliseconds
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new ComprehensivePaymentSecurityTester();
  tester.runAllTests().catch(console.error);
}

module.exports = ComprehensivePaymentSecurityTester;
