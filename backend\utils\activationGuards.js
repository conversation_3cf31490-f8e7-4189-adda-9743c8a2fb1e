const paymentValidator = require('./paymentValidator');
const Subscription = require('../models/subscriptionModel');
const User = require('../models/userModel');

/**
 * ACCOUNT ACTIVATION GUARDS
 * 
 * This utility provides strict guards to prevent account activation
 * until payment is verified as completed and not cancelled/failed.
 * 
 * SECURITY FEATURES:
 * - Prevents activation for cancelled payments
 * - Prevents activation for failed payments
 * - Validates payment status before any activation
 * - Logs all activation attempts for audit
 * - Provides rollback functionality for incorrect activations
 */

class ActivationGuards {
  constructor() {
    this.blockedStatuses = ['CANCELLED', 'FAILED', 'EXPIRED', 'INVALID'];
    this.allowedStatuses = ['COMPLETED', 'PAID'];
  }

  /**
   * Check if account activation is allowed for a subscription
   * @param {Object} subscription - The subscription to check
   * @returns {Object} Guard result
   */
  async checkActivationAllowed(subscription) {
    try {
      console.log(`🛡️ Running activation guards for subscription: ${subscription._id}`);

      // Guard 1: Check subscription exists and has payment history
      if (!subscription || !subscription.paymentHistory || subscription.paymentHistory.length === 0) {
        return this.createGuardResult(false, 'NO_PAYMENT_HISTORY', 'No payment history found for subscription');
      }

      // Guard 2: Get latest payment
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      if (!latestPayment || !latestPayment.orderId) {
        return this.createGuardResult(false, 'INVALID_PAYMENT_DATA', 'Invalid payment data in subscription');
      }

      // Guard 3: Validate payment with external API
      const validationResult = await paymentValidator.validatePayment(latestPayment.orderId);
      
      // Guard 4: Check for blocked payment statuses
      if (this.blockedStatuses.includes(validationResult.status)) {
        console.log(`🚫 ACTIVATION BLOCKED: Payment status is ${validationResult.status}`);
        
        // Log security incident
        await this.logSecurityIncident(subscription, validationResult, 'ACTIVATION_BLOCKED');
        
        return this.createGuardResult(false, 'PAYMENT_STATUS_BLOCKED', 
          `Activation blocked due to payment status: ${validationResult.status}`, {
            paymentStatus: validationResult.status,
            securityFlag: validationResult.securityFlag,
            orderId: latestPayment.orderId
          });
      }

      // Guard 5: Check if payment is completed
      if (!this.allowedStatuses.includes(validationResult.status)) {
        console.log(`⏳ ACTIVATION PENDING: Payment status is ${validationResult.status}`);
        return this.createGuardResult(false, 'PAYMENT_NOT_COMPLETED', 
          `Payment not yet completed. Status: ${validationResult.status}`, {
            paymentStatus: validationResult.status,
            orderId: latestPayment.orderId
          });
      }

      // Guard 6: Check for manual approval requirement
      if (process.env.NODE_ENV === 'production' && !subscription.manuallyApproved) {
        console.log(`🔒 ACTIVATION PENDING: Manual approval required for production`);
        return this.createGuardResult(false, 'MANUAL_APPROVAL_REQUIRED', 
          'Manual admin approval required for activation in production', {
            paymentStatus: validationResult.status,
            orderId: latestPayment.orderId,
            requiresManualApproval: true
          });
      }

      // All guards passed
      console.log(`✅ All activation guards passed for subscription: ${subscription._id}`);
      return this.createGuardResult(true, 'GUARDS_PASSED', 'All activation guards passed', {
        paymentStatus: validationResult.status,
        orderId: latestPayment.orderId,
        validationData: validationResult.paymentData
      });

    } catch (error) {
      console.error(`❌ Error in activation guards:`, error.message);
      
      // Log error as security incident
      await this.logSecurityIncident(subscription, { error: error.message }, 'GUARD_ERROR');
      
      return this.createGuardResult(false, 'GUARD_ERROR', `Guard error: ${error.message}`);
    }
  }

  /**
   * Prevent activation for specific payment statuses
   * @param {string} orderId - Order ID to check
   * @returns {Object} Prevention result
   */
  async preventActivationForStatus(orderId) {
    try {
      const validationResult = await paymentValidator.validatePayment(orderId);
      
      if (validationResult.blockActivation || validationResult.preventActivation) {
        console.log(`🛡️ Activation prevention triggered for order: ${orderId}`);
        console.log(`🚫 Reason: ${validationResult.reason}`);
        console.log(`📊 Status: ${validationResult.status}`);
        
        return {
          prevented: true,
          reason: validationResult.reason,
          status: validationResult.status,
          message: validationResult.message,
          securityFlag: validationResult.securityFlag
        };
      }

      return {
        prevented: false,
        status: validationResult.status,
        message: 'No activation prevention required'
      };

    } catch (error) {
      console.error(`❌ Error in activation prevention:`, error.message);
      return {
        prevented: true,
        reason: 'prevention_error',
        message: `Prevention error: ${error.message}`
      };
    }
  }

  /**
   * Rollback incorrect activation
   * @param {string} subscriptionId - Subscription ID to rollback
   * @param {string} reason - Reason for rollback
   * @returns {Object} Rollback result
   */
  async rollbackActivation(subscriptionId, reason = 'security_rollback') {
    try {
      console.log(`🔄 Rolling back activation for subscription: ${subscriptionId}`);
      console.log(`📋 Reason: ${reason}`);

      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        return {
          success: false,
          message: 'Subscription not found'
        };
      }

      // Deactivate subscription
      subscription.status = 'pending';
      subscription.paymentStatus = 'pending';
      subscription.startDate = null;
      subscription.endDate = null;
      subscription.activatedAt = null;
      subscription.rollbackAt = new Date();
      subscription.rollbackReason = reason;

      await subscription.save();

      // Deactivate user account
      const user = await User.findById(subscription.user);
      if (user) {
        user.subscriptionStatus = 'free';
        user.paymentRequired = true;
        user.subscriptionEndDate = null;
        user.subscriptionActivatedAt = null;
        await user.save();
      }

      // Log rollback
      await this.logSecurityIncident(subscription, { rollbackReason: reason }, 'ACTIVATION_ROLLBACK');

      console.log(`✅ Activation rolled back successfully for subscription: ${subscriptionId}`);
      
      return {
        success: true,
        message: 'Activation rolled back successfully',
        subscriptionId: subscriptionId,
        reason: reason
      };

    } catch (error) {
      console.error(`❌ Error rolling back activation:`, error.message);
      return {
        success: false,
        message: `Rollback error: ${error.message}`
      };
    }
  }

  /**
   * Check for and rollback any incorrectly activated accounts
   * @returns {Object} Audit result
   */
  async auditAndRollbackIncorrectActivations() {
    try {
      console.log('🔍 Auditing for incorrectly activated accounts...');

      // Find active subscriptions
      const activeSubscriptions = await Subscription.find({
        status: 'active',
        paymentStatus: 'paid'
      }).populate('user');

      const rollbackResults = [];

      for (const subscription of activeSubscriptions) {
        if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
          continue;
        }

        const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        const validationResult = await paymentValidator.validatePayment(latestPayment.orderId);

        // Check if this subscription should be rolled back
        if (this.blockedStatuses.includes(validationResult.status)) {
          console.log(`🚨 SECURITY ALERT: Found incorrectly activated subscription ${subscription._id}`);
          console.log(`📊 Payment status: ${validationResult.status}`);
          console.log(`👤 User: ${subscription.user.firstName} ${subscription.user.lastName}`);

          const rollbackResult = await this.rollbackActivation(
            subscription._id, 
            `incorrect_activation_${validationResult.status.toLowerCase()}`
          );

          rollbackResults.push({
            subscriptionId: subscription._id,
            userId: subscription.user._id,
            userName: `${subscription.user.firstName} ${subscription.user.lastName}`,
            paymentStatus: validationResult.status,
            rollbackResult: rollbackResult
          });
        }
      }

      console.log(`✅ Audit completed. Found ${rollbackResults.length} incorrect activations`);
      
      return {
        success: true,
        incorrectActivations: rollbackResults.length,
        rollbackResults: rollbackResults
      };

    } catch (error) {
      console.error(`❌ Error in audit:`, error.message);
      return {
        success: false,
        message: `Audit error: ${error.message}`
      };
    }
  }

  /**
   * Create standardized guard result
   * @param {boolean} allowed - Whether activation is allowed
   * @param {string} code - Result code
   * @param {string} message - Result message
   * @param {Object} data - Additional data
   * @returns {Object} Guard result
   */
  createGuardResult(allowed, code, message, data = {}) {
    return {
      allowed: allowed,
      code: code,
      message: message,
      timestamp: new Date().toISOString(),
      ...data
    };
  }

  /**
   * Log security incident for audit purposes
   * @param {Object} subscription - The subscription
   * @param {Object} incident - Incident data
   * @param {string} type - Incident type
   */
  async logSecurityIncident(subscription, incident, type) {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        type: type,
        subscriptionId: subscription._id,
        userId: subscription.user,
        incident: incident,
        environment: process.env.NODE_ENV
      };

      console.log(`🚨 SECURITY INCIDENT [${type}]:`, JSON.stringify(logEntry, null, 2));

      // In production, this should be stored in a secure audit log database
      // For now, we'll just console log for visibility

    } catch (error) {
      console.error(`❌ Error logging security incident:`, error.message);
    }
  }
}

// Export singleton instance
const activationGuards = new ActivationGuards();
module.exports = activationGuards;
