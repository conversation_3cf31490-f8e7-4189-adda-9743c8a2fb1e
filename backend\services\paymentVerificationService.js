const axios = require('axios');
const Subscription = require('../models/subscriptionModel');
const User = require('../models/userModel');
require('dotenv').config();

class PaymentVerificationService {
  constructor() {
    this.isRunning = false;
    this.checkInterval = 15000; // Check every 15 seconds (more frequent)
    this.maxRetries = 5;
    this.autoFixEnabled = false; // SECURITY: Auto-activation permanently disabled
    this.autoActivationEnabled = false; // SECURITY: No automatic subscription activation
    this.requireStrictVerification = true; // SECURITY: Require strict payment verification
    this.verificationOnly = true; // SECURITY: Only verify, never activate automatically
  }

  // Start the payment verification service
  start() {
    if (this.isRunning) {
      console.log('⚠️ Payment verification service is already running');
      return;
    }

    this.isRunning = true;
    console.log('🚀 Starting payment verification service...');
    console.log(`⏰ Checking every ${this.checkInterval / 1000} seconds`);
    
    this.intervalId = setInterval(() => {
      this.checkPendingPayments();
    }, this.checkInterval);
  }

  // Stop the payment verification service
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.isRunning = false;
      console.log('🛑 Payment verification service stopped');
    }
  }

  // SMART CHECK: Check all pending payments and auto-fix legitimate ones
  async checkPendingPayments() {
    try {
      console.log('🔍 SMART CHECK: Scanning for payment issues...');

      // Find all pending subscriptions created in the last 48 hours (extended window)
      const twoDaysAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);

      const pendingSubscriptions = await Subscription.find({
        paymentStatus: 'pending',
        status: 'pending',
        createdAt: { $gte: twoDaysAgo }
      }).populate('user', 'firstName lastName username email')
        .populate('activePlan', 'title duration discountedPrice');

      // SECURITY: Auto-fixing disabled - only verify payments, don't auto-activate
      console.log(`🔒 SECURITY MODE: Auto-fixing disabled - manual activation required`);

      let totalIssues = pendingSubscriptions.length;
      let verifiedPayments = 0;

      if (pendingSubscriptions.length > 0) {
        console.log(`📋 Found ${pendingSubscriptions.length} pending payments to verify`);

        for (const subscription of pendingSubscriptions) {
          const verificationResult = await this.smartVerifyAndFix(subscription);
          if (verificationResult && verificationResult.verified) {
            verifiedPayments++;
            console.log(`✅ Payment verified for subscription ${subscription._id} - manual activation required`);
          }
          // Wait a bit between requests to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // SECURITY: Skip user status auto-fixing - manual review required
      console.log(`🔒 User status auto-fixing disabled for security`);
      console.log(`📧 Admin review required for any subscription activations`);

      if (totalIssues > 0) {
        console.log(`🔍 SECURITY CHECK RESULTS: Verified ${verifiedPayments}/${totalIssues} payments - manual activation required`);
        if (verifiedPayments > 0) {
          console.log(`📧 ADMIN ALERT: ${verifiedPayments} payments verified and ready for manual activation`);
        }
      } else {
        console.log('✅ No payment issues found - all users have correct status');
      }

    } catch (error) {
      console.error('❌ Error in smart payment check:', error.message);
    }
  }

  // Verify a specific subscription payment with ZenoPay
  async verifySubscriptionPayment(subscription) {
    try {
      if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
        console.log(`⚠️ No payment history for subscription ${subscription._id}`);
        return;
      }

      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      const orderId = latestPayment.orderId;

      console.log(`🔍 Verifying payment for order: ${orderId}`);

      // Check payment status with ZenoPay API
      const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
      
      try {
        const response = await axios.get(statusUrl, {
          headers: {
            "x-api-key": process.env.ZENOPAY_API_KEY,
          },
          timeout: 15000
        });

        console.log(`📊 Payment status response for ${orderId}:`, response.data);

        // SECURITY FIX: Check payment status but don't auto-activate
        if (response.data && response.data.data && response.data.data.length > 0) {
          const paymentData = response.data.data[0];

          if (paymentData.payment_status === 'COMPLETED') {
            console.log(`✅ Payment ${orderId} is COMPLETED! But auto-activation is disabled.`);
            console.log(`🔒 MANUAL ACTIVATION REQUIRED for subscription: ${subscription._id}`);
            console.log(`👤 User: ${subscription.user?.username || subscription.user}`);
            console.log(`📋 Plan: ${subscription.activePlan?.title || 'Unknown'}`);
            console.log(`⚠️ Admin must manually verify and activate this subscription`);

            // Update payment status but don't activate subscription
            if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
              const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
              if (latestPayment.orderId === orderId) {
                latestPayment.paymentStatus = 'paid';
                latestPayment.referenceId = paymentData.reference || `VERIFIED_${Date.now()}`;
                await subscription.save();
                console.log(`💳 Payment history updated for ${orderId}`);
              }
            }
          } else {
            console.log(`⏳ Payment ${orderId} status: ${paymentData.payment_status}`);
          }
        } else {
          console.log(`⏳ No payment data found for ${orderId}`);
        }

      } catch (apiError) {
        console.log(`❌ Error checking payment status for ${orderId}:`, apiError.message);
        
        // If we can't verify with API, check if payment is old enough to auto-activate
        const paymentAge = Date.now() - new Date(subscription.createdAt).getTime();
        const oneHourInMs = 60 * 60 * 1000;
        
        if (paymentAge > oneHourInMs) {
          console.log(`⏰ Payment ${orderId} is over 1 hour old, considering auto-activation`);
          // You could implement auto-activation logic here for old payments
        }
      }

    } catch (error) {
      console.error(`❌ Error verifying subscription ${subscription._id}:`, error.message);
    }
  }

  // SECURITY: Activation method disabled - manual activation required
  async activateSubscription(subscription, paymentData) {
    console.log(`🚫 SECURITY: Automatic activation disabled for subscription ${subscription._id}`);
    console.log(`🔒 MANUAL ACTIVATION REQUIRED - Admin must manually activate this subscription`);
    console.log(`👤 User: ${subscription.user}`);
    console.log(`📋 Plan: ${subscription.activePlan?.title || 'Unknown'}`);
    console.log(`💳 Payment verified but activation blocked for security`);

    // Log the activation attempt for admin review
    try {
      const User = require('../models/userModel');
      const user = await User.findById(subscription.user);

      console.log(`📧 ADMIN ALERT: Manual activation required for:`);
      console.log(`   User: ${user?.firstName} ${user?.lastName} (${user?.username})`);
      console.log(`   Email: ${user?.email}`);
      console.log(`   Phone: ${user?.phoneNumber}`);
      console.log(`   Subscription ID: ${subscription._id}`);
      console.log(`   Plan: ${subscription.activePlan?.title}`);
      console.log(`   Payment Data:`, paymentData);

      // Mark as requiring manual activation
      subscription.requiresManualActivation = true;
      subscription.manualActivationRequestedAt = new Date();
      subscription.paymentVerified = true;

      // Update payment history but don't activate
      if (subscription.paymentHistory.length > 0) {
        const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'verified'; // Verified but not activated
        latestPayment.referenceId = paymentData?.reference || `VERIFIED_PENDING_${Date.now()}`;
        latestPayment.verificationStatus = 'verified_pending_activation';
      }

      await subscription.save();

    } catch (error) {
      console.error(`❌ Error logging activation request:`, error.message);
    }

    return false; // Activation blocked
  }

  // Manual verification for a specific order ID
  async verifyOrderId(orderId) {
    try {
      const subscription = await Subscription.findOne({
        'paymentHistory.orderId': orderId
      }).populate('user', 'firstName lastName username')
        .populate('activePlan', 'title duration discountedPrice');

      if (!subscription) {
        console.log(`❌ No subscription found for order ID: ${orderId}`);
        return false;
      }

      await this.verifySubscriptionPayment(subscription);
      return true;

    } catch (error) {
      console.error(`❌ Error verifying order ${orderId}:`, error.message);
      return false;
    }
  }

  // ENHANCED SECURITY: Strict verification with ZenoPay API confirmation
  async smartVerifyAndFix(subscription) {
    try {
      const timeSinceCreated = Date.now() - new Date(subscription.createdAt).getTime();
      const hoursAgo = Math.floor(timeSinceCreated / (1000 * 60 * 60));

      console.log(`🔍 Smart checking: ${subscription.user?.firstName} ${subscription.user?.lastName} - ${hoursAgo}h ago`);

      // SECURITY ENHANCEMENT: Always require ZenoPay API verification
      console.log(`🔒 Enhanced security mode: All subscriptions require ZenoPay API verification`);

      // Only verify with ZenoPay API, never auto-activate without confirmation
      if (subscription.paymentHistory.length > 0) {
        const payment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        if (payment.orderId && payment.orderId.startsWith('ORDER_')) {
          console.log(`📋 Payment found: ${payment.orderId} - verifying with ZenoPay API`);

          // Verify payment status with ZenoPay API
          const isPaymentCompleted = await this.verifyPaymentWithZenoPayAPI(payment.orderId);

          if (isPaymentCompleted) {
            console.log(`✅ ZenoPay confirmed payment ${payment.orderId} is COMPLETED`);
            await this.logVerifiedPayment(subscription, payment.orderId);
            return { verified: true, orderId: payment.orderId };
          } else {
            console.log(`❌ ZenoPay shows payment ${payment.orderId} is NOT completed`);
            await this.logFailedVerification(subscription, payment.orderId);
            return { verified: false, orderId: payment.orderId };
          }
        }
      }

      console.log(`⚠️ No valid payment found for verification`);
      return { verified: false, orderId: null };
    } catch (error) {
      console.error(`❌ Error in smart verification:`, error.message);
      return { verified: false, error: error.message };
    }
  }

  // SECURITY: User status auto-fixing disabled - manual activation required
  async fixUserStatus(user, subscriptions) {
    console.log(`🔒 SECURITY: Auto-fixing disabled for user ${user.firstName} ${user.lastName} (@${user.username})`);
    console.log(`📧 ADMIN ALERT: Manual review required for user status`);
    console.log(`   User ID: ${user._id}`);
    console.log(`   Current Status: ${user.subscriptionStatus}`);
    console.log(`   Subscriptions: ${subscriptions.length}`);

    // Log subscription details for admin review
    subscriptions.forEach((sub, index) => {
      console.log(`   Subscription ${index + 1}: ${sub.status} / ${sub.paymentStatus} (${sub.activePlan?.title})`);
    });

    console.log(`🔐 Manual activation required - no automatic status changes`);
    return false; // Never auto-fix for security
  }

  // SECURITY FIX: Log pending verifications without auto-activation
  async logPendingVerification(subscription, orderId) {
    try {
      console.log(`📋 PENDING VERIFICATION: Order ${orderId} for user ${subscription.user?.username}`);
      console.log(`   Subscription ID: ${subscription._id}`);
      console.log(`   Created: ${subscription.createdAt}`);
      console.log(`   Plan: ${subscription.activePlan?.title}`);
      console.log(`   ⚠️ REQUIRES MANUAL VERIFICATION BEFORE ACTIVATION`);
    } catch (error) {
      console.error(`❌ Error logging pending verification:`, error.message);
    }
  }

  // ENHANCED SECURITY: Verify payment with ZenoPay API - NO AUTO-ACTIVATION
  async verifySubscriptionPaymentOnly(subscription) {
    try {
      if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
        console.log(`⚠️ No payment history for subscription ${subscription._id}`);
        return { verified: false, reason: 'no_payment_history' };
      }

      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      const orderId = latestPayment.orderId;

      console.log(`🔍 Verifying payment for order: ${orderId} (verification only - NO auto-activation)`);

      return await this.verifyPaymentWithZenoPayAPI(orderId);

    } catch (error) {
      console.error(`❌ Error verifying subscription payment:`, error.message);
      return { verified: false, reason: 'verification_error', error: error.message };
    }
  }

  // ENHANCED: Dedicated ZenoPay API verification method
  async verifyPaymentWithZenoPayAPI(orderId) {
    try {
      if (!orderId || !orderId.startsWith('ORDER_')) {
        console.log(`⚠️ Invalid order ID format: ${orderId}`);
        return { verified: false, reason: 'invalid_order_id' };
      }

      console.log(`🔍 Checking ZenoPay API for order: ${orderId}`);

      // Check payment status with ZenoPay API
      const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;

      const response = await axios.get(statusUrl, {
        headers: {
          "x-api-key": process.env.ZENOPAY_API_KEY,
        },
        timeout: 15000
      });

      console.log(`📊 ZenoPay API response for ${orderId}:`, response.data);

      // Parse ZenoPay response
      if (response.data && response.data.data && response.data.data.length > 0) {
        const paymentData = response.data.data[0];
        const paymentStatus = paymentData.payment_status;

        console.log(`📊 Payment status: ${paymentStatus}`);

        if (paymentStatus === 'COMPLETED') {
          console.log(`✅ Payment ${orderId} is COMPLETED according to ZenoPay`);
          return {
            verified: true,
            status: 'COMPLETED',
            orderId: orderId,
            paymentData: paymentData
          };
        } else if (paymentStatus === 'CANCELLED' || paymentStatus === 'FAILED') {
          console.log(`❌ Payment ${orderId} was ${paymentStatus} - subscription should NOT be activated`);
          return {
            verified: false,
            status: paymentStatus,
            orderId: orderId,
            reason: `payment_${paymentStatus.toLowerCase()}`
          };
        } else {
          console.log(`⏳ Payment ${orderId} is still ${paymentStatus}`);
          return {
            verified: false,
            status: paymentStatus,
            orderId: orderId,
            reason: 'payment_pending'
          };
        }
      } else {
        console.log(`⚠️ No payment data found for ${orderId} in ZenoPay API`);
        return {
          verified: false,
          orderId: orderId,
          reason: 'no_payment_data'
        };
      }

    } catch (apiError) {
      console.log(`❌ ZenoPay API error for ${orderId}:`, apiError.message);
      return {
        verified: false,
        orderId: orderId,
        reason: 'api_error',
        error: apiError.message
      };
    }
  }

  // ENHANCED: Logging methods for payment verification tracking
  async logVerifiedPayment(subscription, orderId) {
    try {
      console.log(`📝 Logging verified payment: ${orderId} for subscription ${subscription._id}`);

      // Update payment history with verification status
      if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
        const payment = subscription.paymentHistory.find(p => p.orderId === orderId);
        if (payment) {
          payment.verificationStatus = 'verified';
          payment.verifiedAt = new Date();
          payment.verificationMethod = 'zenopay_api';
          await subscription.save();
        }
      }
    } catch (error) {
      console.error(`❌ Error logging verified payment:`, error.message);
    }
  }

  async logFailedVerification(subscription, orderId) {
    try {
      console.log(`📝 Logging failed verification: ${orderId} for subscription ${subscription._id}`);

      // Update payment history with failed verification
      if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
        const payment = subscription.paymentHistory.find(p => p.orderId === orderId);
        if (payment) {
          payment.verificationStatus = 'failed';
          payment.verifiedAt = new Date();
          payment.verificationMethod = 'zenopay_api';
          await subscription.save();
        }
      }
    } catch (error) {
      console.error(`❌ Error logging failed verification:`, error.message);
    }
  }

  async logPendingVerification(subscription, orderId) {
    try {
      console.log(`📝 Logging pending verification: ${orderId} for subscription ${subscription._id}`);

      // Update payment history with pending verification
      if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
        const payment = subscription.paymentHistory.find(p => p.orderId === orderId);
        if (payment) {
          payment.verificationStatus = 'pending';
          payment.lastCheckedAt = new Date();
          payment.verificationMethod = 'zenopay_api';
          await subscription.save();
        }
      }
    } catch (error) {
      console.error(`❌ Error logging pending verification:`, error.message);
    }
  }
}

// Export singleton instance
const paymentVerificationService = new PaymentVerificationService();
module.exports = paymentVerificationService;
