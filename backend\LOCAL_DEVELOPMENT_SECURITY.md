# Local Development with Enhanced Payment Security

This document explains how to run the Brainwave application locally with enhanced payment security that prevents account activation for cancelled or failed payments.

## 🔒 Security Features Implemented

### 1. Payment Status Validation
- **Cancelled payments**: NEVER activate accounts
- **Failed payments**: NEVER activate accounts  
- **Completed payments**: Require manual admin approval
- **Unknown statuses**: Blocked by default

### 2. Activation Guards
- Multi-layer validation before any account activation
- Automatic rollback of incorrect activations
- Comprehensive audit logging
- Security incident tracking

### 3. Local Testing System
- Simulate payment flows without external dependencies
- Test cancellation and failure scenarios
- Validate security measures work correctly

## 🚀 Quick Start

### 1. Environment Setup
The `.env` file has been configured for local development:

```bash
NODE_ENV=development
CLIENT_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000
PAYMENT_DEMO_MODE=true
ZENOPAY_ENVIRONMENT=sandbox
```

### 2. Start Local Development Server
```bash
# Option 1: Enhanced local development server
npm run dev:local

# Option 2: Standard development server
npm run dev
```

### 3. Run Security Tests
```bash
# Comprehensive security test suite
npm run test:security

# Payment-specific tests
npm run test:payments

# Audit existing activations
npm run audit:activations
```

## 🛡️ Security Implementation Details

### Payment Validation Flow
1. **Webhook receives payment status**
2. **Activation guards check payment status**
3. **If CANCELLED/FAILED**: Block activation immediately
4. **If COMPLETED**: Validate with ZenoPay API
5. **If valid**: Mark for manual approval
6. **Manual admin approval required for final activation**

### Key Security Files
- `utils/activationGuards.js` - Core security guards
- `utils/paymentValidator.js` - Enhanced payment validation
- `utils/localPaymentTester.js` - Local testing system
- `routes/paymentRoute.js` - Webhook with security integration

### Security Checks
```javascript
// Example: Check if activation is allowed
const guardResult = await activationGuards.checkActivationAllowed(subscription);
if (!guardResult.allowed) {
    // Activation blocked
    console.log(`Blocked: ${guardResult.message}`);
}

// Example: Prevent activation for cancelled payments
const preventionResult = await activationGuards.preventActivationForStatus(orderId);
if (preventionResult.prevented) {
    // Payment was cancelled/failed - block activation
}
```

## 🧪 Testing Payment Security

### 1. Test Cancelled Payment
```bash
node -e "
const tester = require('./utils/localPaymentTester');
(async () => {
  await tester.initializeTestEnvironment();
  const payment = await tester.simulatePaymentInitiation('userId', 'planId');
  await tester.simulatePaymentCancellation(payment.orderId);
  console.log('Cancelled payment test completed');
})();
"
```

### 2. Test Failed Payment
```bash
node -e "
const tester = require('./utils/localPaymentTester');
(async () => {
  await tester.initializeTestEnvironment();
  const payment = await tester.simulatePaymentInitiation('userId', 'planId');
  await tester.simulatePaymentFailure(payment.orderId);
  console.log('Failed payment test completed');
})();
"
```

### 3. Run Comprehensive Tests
```bash
npm run test:security
```

This will run all security tests including:
- Cancelled payment prevention
- Failed payment prevention
- Completed payment flow validation
- Activation guard testing
- Security rollback testing
- Audit and detection testing

## 🔍 Monitoring and Auditing

### View Security Logs
All security events are logged with detailed information:
```bash
# Check server logs for security incidents
tail -f logs/app.log | grep "SECURITY"

# Or check console output for real-time monitoring
```

### Audit Activations
```bash
# Check for incorrectly activated accounts
npm run audit:activations
```

### Manual Activation (Production)
In production, completed payments require manual admin approval:
```javascript
const secureActivation = require('./utils/secureActivation');

// Get pending activations
const pending = await secureActivation.getPendingActivations();

// Manually activate after verification
await secureActivation.manuallyActivateSubscription(
  subscriptionId, 
  adminUserId, 
  'manual_verification_completed'
);
```

## 🚨 Security Alerts

The system will automatically:
1. **Block** cancelled/failed payments from activating accounts
2. **Log** all security incidents with timestamps
3. **Rollback** any incorrectly activated accounts
4. **Alert** administrators of security issues

### Example Security Log
```
🚨 SECURITY INCIDENT [ACTIVATION_BLOCKED]:
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "type": "ACTIVATION_BLOCKED",
  "subscriptionId": "507f1f77bcf86cd799439011",
  "userId": "507f1f77bcf86cd799439012",
  "incident": {
    "paymentStatus": "CANCELLED",
    "securityFlag": "CANCELLED_PAYMENT"
  },
  "environment": "development"
}
```

## 🔧 Configuration Options

### Environment Variables
```bash
# Security Settings
PAYMENT_DEMO_MODE=true          # Enable local testing
NODE_ENV=development            # Development mode
ZENOPAY_ENVIRONMENT=sandbox     # Sandbox mode

# Security Flags
REQUIRE_MANUAL_APPROVAL=true    # Require manual approval
AUTO_ROLLBACK_ENABLED=true      # Enable automatic rollback
SECURITY_AUDIT_ENABLED=true     # Enable security auditing
```

### Local Development Features
- **Mock payment responses** for testing
- **Simulated webhook calls** for payment status changes
- **Automatic test data creation** and cleanup
- **Real-time security monitoring**

## 📋 Troubleshooting

### Common Issues

1. **Payment not being blocked**
   - Check activation guards are properly integrated
   - Verify payment validator is working
   - Review webhook processing logs

2. **Tests failing**
   - Ensure database connection is working
   - Check environment variables are set
   - Verify test data is properly created

3. **Manual activation not working**
   - Check admin permissions
   - Verify subscription status
   - Review payment validation results

### Debug Commands
```bash
# Test payment validator
node -e "const pv = require('./utils/paymentValidator'); pv.validatePayment('ORDER_TEST_123').then(console.log);"

# Test activation guards
node -e "const ag = require('./utils/activationGuards'); ag.checkActivationAllowed(subscription).then(console.log);"

# Check database connection
node -e "const mongoose = require('mongoose'); mongoose.connect(process.env.MONGO_URL).then(() => console.log('DB OK'));"
```

## ✅ Security Checklist

Before deploying to production:

- [ ] All payment security tests pass
- [ ] Cancelled payments are blocked
- [ ] Failed payments are blocked
- [ ] Manual approval is required for completed payments
- [ ] Security audit runs without issues
- [ ] Activation guards are properly integrated
- [ ] Webhook security is validated
- [ ] Admin approval workflow is tested
- [ ] Security logging is working
- [ ] Rollback functionality is tested

## 🎯 Next Steps

1. **Test thoroughly** in local development
2. **Verify security measures** with comprehensive tests
3. **Review admin approval workflow** for production
4. **Set up monitoring** for security incidents
5. **Train administrators** on manual activation process

The system is now configured to run locally with enhanced security that prevents account activation for cancelled or failed payments while requiring manual approval for completed payments.
